import React, { useState, useEffect, useCallback } from 'react'
import { motion } from 'motion/react'
import {
  <PERSON>Check,
  FiX,
  FiRefreshCw,
  FiSettings,
  FiDatabase,
  FiEye,
  FiTable,
  FiGrid
} from 'react-icons/fi'
import { cn } from '../lib/aceternity-utils'
import { useThemeContext } from '../context/useThemeContext'
import { FullWidthLayout, FullWidthSection } from '../components/layout/FullWidthLayout'
import { GlassmorphicCard } from '../components/ui/glassmorphic-card'
import { EnhancedModernTable, EnhancedColumnDef } from '../components/ui/enhanced-modern-table'
import { EditableTransactionNode } from '../components/graph/nodes/EditableTransactionNode'
import { testUnifiedAppIntegration, monitorAppPerformance } from '../utils/unifiedAppUtils'
import UnifiedNavbar from '../components/UnifiedNavbar'

interface TestResult {
  test: string
  passed: boolean
  error?: string
}

interface PerformanceMetrics {
  renderTime: number
  memoryUsage: number
  dbQueryTime: number
}

const IntegrationTest: React.FC = () => {
  const { isDark, toggleTheme } = useThemeContext()
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [overallSuccess, setOverallSuccess] = useState<boolean | null>(null)
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null)
  const [componentTests, setComponentTests] = useState({
    glassmorphism: false,
    tables: false,
    graphs: false,
    layouts: false,
    database: false
  })

  // Run integration tests
  const runTests = useCallback(async (): Promise<void> => {
    setIsRunning(true)
    setTestResults([])
    setOverallSuccess(null)

    try {
      // Start performance monitoring
      const metrics = monitorAppPerformance()

      // Run unified app integration tests
      const { success, results } = await testUnifiedAppIntegration()

      setTestResults(results)
      setOverallSuccess(success)
      setPerformanceMetrics(metrics)

      // Test individual components
      await testComponents()
    } catch (error) {
      console.error('Integration test failed:', error)
      setTestResults([{ test: 'Integration Test Suite', passed: false, error: String(error) }])
      setOverallSuccess(false)
    } finally {
      setIsRunning(false)
    }
  }, [])

  // Test individual components
  const testComponents = useCallback(async (): Promise<void> => {
    const tests = { ...componentTests }

    // Test glassmorphism support
    try {
      const testEl = document.createElement('div')
      testEl.style.backdropFilter = 'blur(10px)'
      tests.glassmorphism = testEl.style.backdropFilter === 'blur(10px)'
    } catch {
      tests.glassmorphism = false
    }

    // Test table functionality
    try {
      tests.tables = typeof EnhancedModernTable === 'function'
    } catch {
      tests.tables = false
    }

    // Test graph nodes
    try {
      tests.graphs = typeof EditableTransactionNode === 'function'
    } catch {
      tests.graphs = false
    }

    // Test layout components
    try {
      tests.layouts = typeof FullWidthLayout === 'function'
    } catch {
      tests.layouts = false
    }

    // Test database connectivity
    try {
      await window.api.database.getComplaints()
      tests.database = true
    } catch {
      tests.database = false
    }

    setComponentTests(tests)
  }, [])

  // Auto-run tests on mount
  useEffect(() => {
    runTests()
  }, [runTests])

  // Sample data for component testing
  const sampleTableData = [
    {
      id: '1',
      name: 'Test Complaint 1',
      amount: 50000,
      status: 'processed',
      date: '2024-01-15'
    },
    {
      id: '2',
      name: 'Test Complaint 2',
      amount: 75000,
      status: 'pending',
      date: '2024-01-16'
    }
  ]

  const sampleTableColumns: EnhancedColumnDef<(typeof sampleTableData)[0]>[] = [
    { key: 'name', title: 'Name', type: 'text' as const, editable: true },
    { key: 'amount', title: 'Amount', type: 'number' as const, editable: true },
    { key: 'status', title: 'Status', type: 'text' as const, editable: true },
    { key: 'date', title: 'Date', type: 'date' as const, editable: true }
  ]

  return (
    <FullWidthLayout
      enableLampBackground={false}
      enableGlassmorphism={true}
      maxWidth="none"
      padding="none"
    >
      <UnifiedNavbar
        title="Integration Test Suite"
        showBackButton={true}
        customActions={
          <div className="flex items-center gap-2">
            <button
              onClick={toggleTheme}
              className={cn(
                'p-2 rounded-lg transition-colors',
                isDark
                  ? 'bg-white/10 hover:bg-white/20 text-white'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-900'
              )}
              title="Toggle theme"
            >
              {isDark ? '🌙' : '☀️'}
            </button>
            <button
              onClick={runTests}
              disabled={isRunning}
              className={cn(
                'flex items-center gap-2 px-4 py-2 rounded-lg transition-colors',
                'bg-blue-600 hover:bg-blue-700 text-white',
                'disabled:opacity-50 disabled:cursor-not-allowed'
              )}
            >
              <FiRefreshCw className={cn('w-4 h-4', isRunning && 'animate-spin')} />
              {isRunning ? 'Running Tests...' : 'Run Tests'}
            </button>
          </div>
        }
      />

      <main className="flex-1 p-6 space-y-8">
        {/* Test Results Overview */}
        <FullWidthSection
          title="Test Results"
          subtitle="Integration test results and system status"
        >
          <div className="p-6 space-y-6">
            {/* Overall Status */}
            <div className="flex items-center justify-center p-8">
              <div
                className={cn(
                  'flex items-center gap-4 px-6 py-4 rounded-xl',
                  overallSuccess === true &&
                    'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200',
                  overallSuccess === false &&
                    'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200',
                  overallSuccess === null &&
                    'bg-gray-100 dark:bg-gray-800/20 text-gray-800 dark:text-gray-200'
                )}
              >
                {overallSuccess === true && <FiCheck className="w-6 h-6" />}
                {overallSuccess === false && <FiX className="w-6 h-6" />}
                {overallSuccess === null && <FiRefreshCw className="w-6 h-6" />}
                <span className="text-lg font-semibold">
                  {overallSuccess === true && 'All Tests Passed'}
                  {overallSuccess === false && 'Some Tests Failed'}
                  {overallSuccess === null && 'Tests Not Run'}
                </span>
              </div>
            </div>

            {/* Individual Test Results */}
            {testResults.length > 0 && (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {testResults.map((result, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={cn(
                      'p-4 rounded-lg border',
                      result.passed
                        ? 'bg-green-50 dark:bg-green-900/10 border-green-200 dark:border-green-800'
                        : 'bg-red-50 dark:bg-red-900/10 border-red-200 dark:border-red-800'
                    )}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      {result.passed ? (
                        <FiCheck className="w-4 h-4 text-green-600 dark:text-green-400" />
                      ) : (
                        <FiX className="w-4 h-4 text-red-600 dark:text-red-400" />
                      )}
                      <span className="font-medium">{result.test}</span>
                    </div>
                    {result.error && (
                      <p className="text-sm text-red-600 dark:text-red-400">{result.error}</p>
                    )}
                  </motion.div>
                ))}
              </div>
            )}

            {/* Component Tests */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
              {Object.entries(componentTests).map(([component, passed]) => (
                <div
                  key={component}
                  className={cn(
                    'p-4 rounded-lg border text-center',
                    passed
                      ? 'bg-green-50 dark:bg-green-900/10 border-green-200 dark:border-green-800'
                      : 'bg-red-50 dark:bg-red-900/10 border-red-200 dark:border-red-800'
                  )}
                >
                  <div className="flex justify-center mb-2">
                    {component === 'glassmorphism' && <FiEye className="w-6 h-6" />}
                    {component === 'tables' && <FiTable className="w-6 h-6" />}
                    {component === 'graphs' && <FiGrid className="w-6 h-6" />}
                    {component === 'layouts' && <FiSettings className="w-6 h-6" />}
                    {component === 'database' && <FiDatabase className="w-6 h-6" />}
                  </div>
                  <div className="font-medium capitalize">{component}</div>
                  <div
                    className={cn(
                      'text-sm',
                      passed
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-red-600 dark:text-red-400'
                    )}
                  >
                    {passed ? 'Working' : 'Failed'}
                  </div>
                </div>
              ))}
            </div>

            {/* Performance Metrics */}
            {performanceMetrics && (
              <div className="grid gap-4 md:grid-cols-3">
                <div className="p-4 rounded-lg bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800">
                  <div className="text-sm text-blue-600 dark:text-blue-400">Render Time</div>
                  <div className="text-lg font-semibold">
                    {performanceMetrics.renderTime.toFixed(2)}ms
                  </div>
                </div>
                <div className="p-4 rounded-lg bg-purple-50 dark:bg-purple-900/10 border border-purple-200 dark:border-purple-800">
                  <div className="text-sm text-purple-600 dark:text-purple-400">Memory Usage</div>
                  <div className="text-lg font-semibold">
                    {performanceMetrics.memoryUsage.toFixed(2)}MB
                  </div>
                </div>
                <div className="p-4 rounded-lg bg-orange-50 dark:bg-orange-900/10 border border-orange-200 dark:border-orange-800">
                  <div className="text-sm text-orange-600 dark:text-orange-400">DB Query Time</div>
                  <div className="text-lg font-semibold">
                    {performanceMetrics.dbQueryTime.toFixed(2)}ms
                  </div>
                </div>
              </div>
            )}
          </div>
        </FullWidthSection>

        {/* Component Showcase */}
        <FullWidthSection
          title="Component Showcase"
          subtitle="Live demonstration of enhanced components"
        >
          <div className="p-6 space-y-8">
            {/* Glassmorphic Cards */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Glassmorphic Cards</h3>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                <GlassmorphicCard enableGlow={true} enableTracingBeam={false}>
                  <h4 className="font-semibold mb-2">Enhanced Card</h4>
                  <p className="text-sm opacity-80">
                    This card demonstrates glassmorphism effects with backdrop blur and hover glow.
                  </p>
                </GlassmorphicCard>
                <GlassmorphicCard enableGlow={false} enableTracingBeam={false}>
                  <h4 className="font-semibold mb-2">Simple Card</h4>
                  <p className="text-sm opacity-80">
                    This card shows the basic glassmorphism without glow effects.
                  </p>
                </GlassmorphicCard>
                <GlassmorphicCard enableGlow={true} enableTracingBeam={true}>
                  <h4 className="font-semibold mb-2">Tracing Beam Card</h4>
                  <p className="text-sm opacity-80">
                    This card includes the tracing beam component for enhanced visual appeal.
                  </p>
                </GlassmorphicCard>
              </div>
            </div>

            {/* Enhanced Table */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Enhanced Modern Table</h3>
              <EnhancedModernTable
                data={sampleTableData}
                columns={sampleTableColumns}
                config={{
                  enableSearch: true,
                  enablePagination: true,
                  enableSorting: true,
                  enableGlassmorphism: true,
                  pageSize: 5
                }}
                operations={{
                  onCreate: async (data) => ({ ...data, id: Date.now().toString() }),
                  onUpdate: async (id, data) => ({
                    ...sampleTableData.find((item) => item.id === id)!,
                    ...data
                  }),
                  onDelete: async (id) => console.log('Delete:', id)
                }}
              />
            </div>
          </div>
        </FullWidthSection>
      </main>
    </FullWidthLayout>
  )
}

export default IntegrationTest
