// Utility functions and types for UnifiedAppProvider
import React, { createContext } from 'react'

// Theme types
export type Theme = 'light' | 'dark'

// Notification types
export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  timestamp: Date
  duration?: number
}

// App state interface
export interface AppState {
  isLoading: boolean
  error: string | null
  notifications: Notification[]
  theme: Theme
  sidebarCollapsed: boolean
  currentPage: string
}

// Context type
export interface UnifiedAppContextType {
  state: AppState
  dispatch: React.Dispatch<AppAction>
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void
  removeNotification: (id: string) => void
  setTheme: (theme: Theme) => void
  toggleSidebar: () => void
  setCurrentPage: (page: string) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
}

// Action types
export type AppAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'ADD_NOTIFICATION'; payload: Notification }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'SET_THEME'; payload: Theme }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_CURRENT_PAGE'; payload: string }

// Create context
export const UnifiedAppContext = createContext<UnifiedAppContextType | null>(null)

// Hook to use the unified app context
export const useUnifiedApp = (): UnifiedAppContextType => {
  const context = React.useContext(UnifiedAppContext)
  if (!context) {
    throw new Error('useUnifiedApp must be used within a UnifiedAppProvider')
  }
  return context
}

// Integration testing utilities
export const testUnifiedAppIntegration = async (): Promise<{
  success: boolean
  results: Array<{ test: string; passed: boolean; error?: string }>
}> => {
  const results: Array<{ test: string; passed: boolean; error?: string }> = []

  try {
    // Test 1: Context availability
    results.push({
      test: 'UnifiedAppContext availability',
      passed: UnifiedAppContext !== null
    })

    // Test 2: Theme switching capability
    const testElement = document.createElement('div')
    testElement.className = 'test-theme-element'
    document.body.appendChild(testElement)

    // Apply dark theme
    testElement.setAttribute('data-theme', 'dark')
    const darkThemeApplied = testElement.getAttribute('data-theme') === 'dark'

    // Apply light theme
    testElement.setAttribute('data-theme', 'light')
    const lightThemeApplied = testElement.getAttribute('data-theme') === 'light'

    document.body.removeChild(testElement)

    results.push({
      test: 'Theme switching capability',
      passed: darkThemeApplied && lightThemeApplied
    })

    // Test 3: Notification system
    const testNotification: Omit<Notification, 'id' | 'timestamp'> = {
      type: 'info',
      title: 'Test',
      message: 'Test notification'
    }

    results.push({
      test: 'Notification creation',
      passed: testNotification.type === 'info' && testNotification.title === 'Test'
    })

    // Test 4: Glassmorphism support
    const styles = getComputedStyle(document.documentElement)
    results.push({
      test: 'Component rendering capability',
      passed:
        styles.backdropFilter !== undefined || 
        (styles as { webkitBackdropFilter?: string }).webkitBackdropFilter !== undefined
    })
  } catch (error) {
    results.push({
      test: 'Component rendering capability',
      passed: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  const success = results.every((result) => result.passed)
  return { success, results }
}

// Performance monitoring utilities
export const monitorAppPerformance = (): {
  renderTime: number
  memoryUsage: number
  dbQueryTime: number
} => {
  const metrics = {
    renderTime: 0,
    memoryUsage: 0,
    dbQueryTime: 0
  }

  // Monitor render time
  if (performance.mark) {
    performance.mark('app-render-start')
    setTimeout(() => {
      performance.mark('app-render-end')
      performance.measure('app-render', 'app-render-start', 'app-render-end')
      const measure = performance.getEntriesByName('app-render')[0]
      metrics.renderTime = measure?.duration || 0
    }, 0)
  }

  // Monitor memory usage (if available)
  if ('memory' in performance) {
    const memInfo = (performance as { memory?: { usedJSHeapSize: number } }).memory
    if (memInfo) {
      metrics.memoryUsage = memInfo.usedJSHeapSize / 1024 / 1024 // MB
    }
  }

  // Mock database query time (replace with actual implementation)
  metrics.dbQueryTime = Math.random() * 100

  return metrics
}
